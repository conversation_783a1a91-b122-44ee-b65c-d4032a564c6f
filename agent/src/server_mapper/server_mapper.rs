use std::collections::HashMap;
use std::sync::{Arc, Weak};
use tokio::sync::RwLock;
use log::{debug, warn};

// 前向声明，避免循环依赖
use crate::server::server_handler::ServerHandler;

/// 服务器映射器
///
/// 负责管理客户端与服务器之间的连接映射关系，实现数据转发功能。
/// 该结构体维护一个二级映射表：客户端UUID -> 服务器UUID -> 服务器处理器弱引用
///
/// # 数据结构说明
/// ```
/// mapper: {
///     "client_uuid_1": {
///         "server_uuid_1": Weak<ServerHandler>,
///         "server_uuid_2": Weak<ServerHandler>,
///     },
///     "client_uuid_2": {
///         "server_uuid_3": Weak<ServerHandler>,
///     }
/// }
/// ```
///
/// # 使用场景
/// - 客户端连接时注册映射关系
/// - 服务器需要向特定客户端转发数据
/// - 客户端断开连接时清理映射关系
pub struct ServerMapper {
    /// 二级映射表：客户端UUID -> (服务器UUID -> 服务器处理器弱引用)
    /// 使用RwLock确保并发安全，支持多读单写
    mapper: RwLock<HashMap<String, HashMap<String, Weak<ServerHandler>>>>,
}

impl ServerMapper {
    /// 创建新的服务器映射器实例
    ///
    /// # 返回值
    /// 返回初始化完成的ServerMapper实例
    pub fn new() -> Self {
        Self {
            mapper: RwLock::new(HashMap::new()),
        }
    }

    /// 添加客户端与服务器的映射关系
    ///
    /// 为指定的客户端UUID添加服务器连接映射。如果客户端UUID不存在，
    /// 会创建新的映射表；如果已存在但服务器UUID不存在，则添加新的服务器映射。
    ///
    /// # 参数
    /// * `client_uuid` - 客户端唯一标识符
    /// * `server_uuid` - 服务器唯一标识符
    /// * `server_handler` - 服务器处理器的弱引用
    ///
    /// # 示例
    /// ```rust
    /// let mapper = ServerMapper::new();
    /// let server_handler = Arc::new(ServerHandler::new(...));
    ///
    /// mapper.add_mapper(
    ///     &"client_123".to_string(),
    ///     &"server_456".to_string(),
    ///     Arc::downgrade(&server_handler)
    /// ).await;
    /// ```
    pub async fn add_mapper(
        &self,
        client_uuid: &str,
        server_uuid: &str,
        server_handler: Weak<ServerHandler>,
    ) {
        let mut mapper = self.mapper.write().await;

        // 检查客户端映射是否已存在
        if let Some(server_map) = mapper.get_mut(client_uuid) {
            // 客户端映射存在，检查服务器映射是否已存在
            if !server_map.contains_key(server_uuid) {
                server_map.insert(server_uuid.to_string(), server_handler);
                debug!("为客户端 {} 添加服务器映射: {}", client_uuid, server_uuid);
            } else {
                debug!("服务器映射已存在: {} -> {}", client_uuid, server_uuid);
            }
        } else {
            // 客户端映射不存在，创建新的映射表
            let mut server_map = HashMap::new();
            server_map.insert(server_uuid.to_string(), server_handler);
            mapper.insert(client_uuid.to_string(), server_map);
            debug!("为客户端 {} 创建新映射并添加服务器: {}", client_uuid, server_uuid);
        }
    }

    /// 删除客户端与服务器的映射关系
    ///
    /// 移除指定客户端UUID下的特定服务器映射。如果删除后客户端下
    /// 没有任何服务器映射，则同时删除整个客户端映射。
    ///
    /// # 参数
    /// * `client_uuid` - 客户端唯一标识符
    /// * `server_uuid` - 服务器唯一标识符
    ///
    /// # 示例
    /// ```rust
    /// mapper.del_mapper(
    ///     &"client_123".to_string(),
    ///     &"server_456".to_string()
    /// ).await;
    /// ```
    pub async fn del_mapper(&self, client_uuid: &str, server_uuid: &str) {
        let mut mapper = self.mapper.write().await;

        // 查找客户端映射
        if let Some(server_map) = mapper.get_mut(client_uuid) {
            // 移除指定的服务器映射
            if server_map.remove(server_uuid).is_some() {
                debug!("删除服务器映射: {} -> {}", client_uuid, server_uuid);

                // 如果客户端下没有任何服务器映射，删除整个客户端映射
                if server_map.is_empty() {
                    mapper.remove(client_uuid);
                    debug!("客户端 {} 下无服务器映射，删除客户端映射", client_uuid);
                }
            } else {
                debug!("服务器映射不存在: {} -> {}", client_uuid, server_uuid);
            }
        } else {
            debug!("客户端映射不存在: {}", client_uuid);
        }
    }

    /// 向指定客户端转发数据
    ///
    /// 查找目标客户端的所有服务器连接，尝试通过任意一个可用的连接
    /// 发送数据。采用"发送成功即停止"的策略，确保数据只发送一次。
    ///
    /// # 参数
    /// * `target_client_uuid` - 目标客户端唯一标识符
    /// * `data` - 要转发的数据
    ///
    /// # 返回值
    /// 返回是否成功发送数据
    ///
    /// # 注意事项
    /// - 如果客户端有多个服务器连接，只会通过第一个成功的连接发送
    /// - 如果所有连接都发送失败，数据将丢失
    /// - 会自动清理失效的弱引用
    ///
    /// # 示例
    /// ```rust
    /// let data = vec![1, 2, 3, 4];
    /// let success = mapper.forward_data(
    ///     &"client_123".to_string(),
    ///     data
    /// ).await;
    /// ```
    pub async fn forward_data(&self, target_client_uuid: &str, data: Vec<u8>) -> bool {
        let mut mapper = self.mapper.write().await;

        // 查找目标客户端的服务器映射
        if let Some(server_map) = mapper.get_mut(target_client_uuid) {
            if server_map.is_empty() {
                debug!("客户端 {} 没有可用的服务器连接", target_client_uuid);
                return false;
            }

            // 收集需要移除的失效引用
            let mut keys_to_remove = Vec::new();
            let mut success = false;

            // 尝试通过任意一个可用连接发送数据
            for (server_uuid, weak_handler) in server_map.iter() {
                if let Some(handler) = weak_handler.upgrade() {
                    if handler.send_binary_data(data.clone()).await {
                        debug!("数据转发成功: {} -> {} 字节 (通过服务器 {})",
                               target_client_uuid, data.len(), server_uuid);
                        success = true;
                        break;
                    } else {
                        debug!("数据转发失败: {} -> 服务器 {}, 连接不可用",
                              target_client_uuid, server_uuid);
                    }
                } else {
                    // 弱引用已失效，标记为需要移除
                    keys_to_remove.push(server_uuid.clone());
                    debug!("服务器处理器已失效: {} -> {}", target_client_uuid, server_uuid);
                }
            }

            // 移除失效的引用
            for key in keys_to_remove {
                server_map.remove(&key);
            }

            // 如果服务器映射为空，移除整个客户端映射
            if server_map.is_empty() {
                mapper.remove(target_client_uuid);
                debug!("客户端 {} 的所有服务器连接都已失效，删除客户端映射", target_client_uuid);
            }

            if !success {
                debug!("所有服务器连接都无法转发数据到客户端: {}", target_client_uuid);
            }
            success
        } else {
            debug!("目标客户端映射不存在: {}", target_client_uuid);
            false
        }
    }
}
